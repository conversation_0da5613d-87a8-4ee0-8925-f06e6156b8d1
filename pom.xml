<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.hl</groupId>
    <artifactId>basic</artifactId>
    <version>*******-SNAPSHOT</version>
    <relativePath></relativePath>
  </parent>

  <groupId>com.hl</groupId>
  <artifactId>hl-wj-police-archive</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>hl-wj-police-archive</name>
  <description>武进民警全息档案</description>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>



    <dependency>
      <groupId>com.hl</groupId>
      <artifactId>hl-basic</artifactId>
    </dependency>

    <dependency>
      <groupId>com.hl</groupId>
      <artifactId>hl-sso</artifactId>
      <version>*******-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>


    <dependency>
      <groupId>com.hl</groupId>
      <artifactId>hl-dict-basic</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>


    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc8</artifactId>
    </dependency>


    <dependency>
      <groupId>cn.easyproject</groupId>
      <artifactId>orai18n</artifactId>
      <version>********.0</version>
    </dependency>


    <dependency>
      <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
       <version>1.18.30</version>
    </dependency>


    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
      <version>0.2.0</version>
    </dependency>

    <dependency>
      <groupId>io.github.linpeilie</groupId>
      <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
      <version>1.4.8</version>
    </dependency>

    <dependency>
      <groupId>com.aizuda</groupId>
      <artifactId>snail-job-client-starter</artifactId>
        <version>1.7.1-jdk8</version>
    </dependency>

    <dependency>
      <groupId>com.aizuda</groupId>
      <artifactId>snail-job-client-job-core</artifactId>
        <version>1.7.1-jdk8</version>
    </dependency>

    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.21.12</version>
    </dependency>

    <!-- 引入easy-es最新版本的依赖-->
    <dependency>
      <groupId>org.dromara.easy-es</groupId>
      <artifactId>easy-es-boot-starter</artifactId>
      <!--这里Latest Version是指最新版本的依赖,比如3.0.0,可以通过下面的图片获取-->
      <version>3.0.0</version>
    </dependency>

    <dependency>
      <groupId>org.glassfish</groupId>
      <artifactId>jakarta.json</artifactId>
      <version>2.0.1</version>
    </dependency>

    <!-- 如果有依赖冲突,导致底层es相关依赖非7.17.28,需要参考避坑指南章节文档先排除springboot中内置的es依赖-->

    <dependency>
      <groupId>com.deepoove</groupId>
      <artifactId>poi-tl</artifactId>
      <version>1.12.2</version>
    </dependency>

    <dependency>
      <groupId>co.elastic.clients</groupId>
      <artifactId>elasticsearch-java</artifactId>
      <version>7.17.28</version>
    </dependency>

    <!-- 显式添加 Elasticsearch Geo 库（7.17.28） -->
    <dependency>
      <groupId>org.elasticsearch</groupId>
      <artifactId>elasticsearch-geo</artifactId>
      <version>7.17.28</version>
    </dependency>

    <!-- 显式添加 Elasticsearch 核心库（7.17.28） -->
    <dependency>
      <groupId>org.elasticsearch</groupId>
      <artifactId>elasticsearch</artifactId>
      <version>7.17.28</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
      <exclusions>
        <!-- 排除旧版 High Level Rest Client -->
        <exclusion>
          <groupId>org.elasticsearch.client</groupId>
          <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </exclusion>
        <!-- 排除旧版 Elasticsearch 核心库 -->
        <exclusion>
          <groupId>org.elasticsearch</groupId>
          <artifactId>elasticsearch</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>elasticsearch-java</artifactId>
          <groupId>co.elastic.clients</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-amqp</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.idev.excel</groupId>
      <artifactId>fastexcel</artifactId>
      <version>1.2.0</version>
    </dependency>

    <dependency>
      <groupId>com.hl</groupId>
      <artifactId>hl-dict</artifactId>
      <version>*******-SNAPSHOT</version>
    </dependency>

      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-actuator</artifactId>
      </dependency>

  </dependencies>

  <repositories>
    <repository>
      <id>company</id>
      <url>http://192.168.10.104:10000/repository/maven-public/</url>
    </repository>
  </repositories>

  <build>
<!--    <defaultGoal>package</defaultGoal>-->
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
          <showWarnings>true</showWarnings>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>1.18.30</version>
            </path>
            <path>
              <groupId>io.github.linpeilie</groupId>
              <artifactId>mapstruct-plus-processor</artifactId>
              <version>1.4.8</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
          </annotationProcessorPaths>
          <compilerArgs>
            <arg>-Xlint:unchecked</arg>
            <arg>-Xlint:deprecation</arg>
          </compilerArgs>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${pom.basedir}/target/libs</outputDirectory>
              <overWriteReleases>false</overWriteReleases>
              <overWriteSnapshots>false</overWriteSnapshots>
              <overWriteIfNewer>true</overWriteIfNewer>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.22.2</version>
        <configuration>
          <skipTests>true</skipTests>
        </configuration>
      </plugin>
    </plugins>
  </build>


</project>
