package com.hl.archive.listener;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.config.TaskListenerConfig;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import com.hl.archive.listener.strategy.TaskHandleStrategyFactory;
import com.hl.common.domain.R;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "task-listener", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TaskEventListener {

    private final TaskHandleStrategyFactory strategyFactory;
    private final TaskListenerConfig taskListenerConfig;


    @RabbitListener(queues = "#{taskListenerConfig.queueName}")
    public R<?> rabbitListener(Message message, Channel channel) throws IOException {
        try {
            String msgBody = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接收到消息: {}", msgBody);
            ThreadUtil.execAsync(() -> {
                handleData(msgBody);
            });
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            // 处理失败，是否需要重试或丢弃可根据业务设定
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
        return R.ok();
    }


    private void handleData(String msg) {
        try {
            JSONObject data = JSONObject.parseObject(msg);
            JSONObject contentData = JSONObject.parseObject(data.getString("content_data"));
            String opt = contentData.getString("opt");

            String configUuid = (String) contentData.getByPath("data.config_uuid");
            if (StrUtil.isBlank(configUuid)) {
                log.warn("配置UUID为空，跳过处理");
                return;
            }

            // 使用策略模式处理任务
            TaskHandleStrategy strategy = strategyFactory.getStrategy(configUuid);
            if (strategy != null) {
                log.info("使用策略处理任务: configUuid={}, opt={}, strategy={}",
                        configUuid, opt, strategy.getClass().getSimpleName());
                strategy.handle(contentData, opt);
            } else {
                log.warn("未找到对应的处理策略: configUuid={}", configUuid);
            }
        } catch (Exception e) {
            log.error("处理任务数据失败: {}", e.getMessage(), e);
        }
    }



}
