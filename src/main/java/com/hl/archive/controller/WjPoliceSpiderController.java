package com.hl.archive.controller;

import com.hl.archive.domain.dto.WjPoliceSpiderResult;
import com.hl.archive.service.FjWebSiteSpiderService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 武进公安信息网爬虫控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/spider/wj-police")
@RequiredArgsConstructor
@Api(tags = "武进公安信息网爬虫")
public class WjPoliceSpiderController {

    private final FjWebSiteSpiderService fjWebSiteSpiderService;

    /**
     * 爬取武进公安信息网所有数据
     */
    @PostMapping("/crawl-all")
    @ApiOperation("爬取所有页面数据")
    public R<WjPoliceSpiderResult> crawlAllPages(
            @ApiParam("基础URL，为空时使用默认URL") 
            @RequestParam(required = false) String baseUrl) {
        
        try {
            log.info("开始爬取武进公安信息网数据，baseUrl: {}", baseUrl);
            WjPoliceSpiderResult result = fjWebSiteSpiderService.crawlWjPoliceNews(baseUrl);
            
            if (result.getSuccess()) {
                log.info("爬取成功，共{}页，{}条数据", result.getTotalPages(), result.getNewsList().size());
                return R.ok(result);
            } else {
                log.error("爬取失败: {}", result.getErrorMessage());
                return R.fail(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("爬取武进公安信息网数据异常", e);
            return R.fail("爬取失败: " + e.getMessage());
        }
    }

    /**
     * 爬取指定页面数据
     */
    @PostMapping("/crawl-page")
    @ApiOperation("爬取指定页面数据")
    public R<WjPoliceSpiderResult> crawlSinglePage(
            @ApiParam("基础URL，为空时使用默认URL") 
            @RequestParam(required = false) String baseUrl,
            @ApiParam("页码") 
            @RequestParam(defaultValue = "1") int page) {
        
        try {
            log.info("开始爬取第{}页数据，baseUrl: {}", page, baseUrl);
            WjPoliceSpiderResult result = fjWebSiteSpiderService.crawlSinglePageOnly(baseUrl, page);
            
            if (result.getSuccess()) {
                log.info("第{}页爬取成功，获取{}条数据", page, result.getNewsList().size());
                return R.ok(result);
            } else {
                log.error("第{}页爬取失败: {}", page, result.getErrorMessage());
                return R.fail(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("爬取第{}页数据异常", page, e);
            return R.fail("爬取失败: " + e.getMessage());
        }
    }

    /**
     * 获取第八次"登锋"砺警学堂信息
     */
    @GetMapping("/eighth-dengfeng")
    @ApiOperation("获取第八次登锋砺警学堂信息")
    public R<WjPoliceSpiderResult> getEighthDengfengNews(
            @ApiParam("基础URL，为空时使用默认URL") 
            @RequestParam(required = false) String baseUrl) {
        
        try {
            log.info("开始查找第八次登锋砺警学堂信息");
            WjPoliceSpiderResult result = fjWebSiteSpiderService.crawlWjPoliceNews(baseUrl);
            
            if (result.getSuccess()) {
                if (result.getEighthDengfengNews() != null) {
                    log.info("找到第八次登锋砺警学堂: {}", result.getEighthDengfengNews().getTitle());
                    // 只返回第八次登锋的信息
                    WjPoliceSpiderResult singleResult = new WjPoliceSpiderResult();
                    singleResult.setSuccess(true);
                    singleResult.setEighthDengfengNews(result.getEighthDengfengNews());
                    singleResult.setTotalPages(result.getTotalPages());
                    singleResult.setTotalRecords(result.getTotalRecords());
                    return R.ok(singleResult);
                } else {
                    return R.fail("未找到第八次登锋砺警学堂信息");
                }
            } else {
                return R.fail(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("查找第八次登锋砺警学堂信息异常", e);
            return R.fail("查找失败: " + e.getMessage());
        }
    }
}
