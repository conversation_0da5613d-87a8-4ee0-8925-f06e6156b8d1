package com.hl.archive.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计数字穿透查询请求
 */
@Data
@ApiModel(description = "统计数字穿透查询请求")
public class StatisticsDrillDownRequest {

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 统计类型（可选，用于单一条件查询）
     * total_count: 总人数
     * age_20_30: 20-30岁
     * age_30_40: 30-40岁
     * age_40_50: 40-50岁
     * age_50_up: 50岁以上
     *
     * 民警学历分类:
     * edu_zhuan: 大专学历
     * edu_ben: 本科学历
     * edu_shuo: 硕士学历
     * edu_boshi: 博士学历
     *
     * 辅警学历分类:
     * edu_junior_and_below: 初中及以下学历
     * edu_technical: 中专学历
     * edu_high_school: 高中学历
     * edu_college: 大专学历
     * edu_bachelor_and_above: 本科及以上学历
     *
     * maleCount: 男性人数
     * femaleCount: 女性人数
     * onDutyCount: 在岗人数
     * offDutyCount: 不在岗人数
     */
    @ApiModelProperty(value = "统计类型（可选，用于单一条件查询）")
    private String statisticsType;

    // ========== 自由组合查询条件 ==========

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String educationLevel;

    /**
     * 性别(1:男,2:女)
     */
    @ApiModelProperty(value = "性别(1:男,2:女)")
    private Byte gender;

    /**
     * 年龄范围 - 最小年龄
     */
    @ApiModelProperty(value = "最小年龄")
    private Integer minAge;

    /**
     * 年龄范围 - 最大年龄
     */
    @ApiModelProperty(value = "最大年龄")
    private Integer maxAge;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String politicalIdentity;

    /**
     * 职务职级
     */
    @ApiModelProperty(value = "职务职级")
    private String positionName;

    /**
     * 在岗状态 (0:在岗, 非0:不在岗)
     */
    @ApiModelProperty(value = "在岗状态 (0:在岗, 非0:不在岗)")
    private Integer dutyStatus;

    /**
     * 页码（从1开始）
     */
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer limit = 20;

    private String name;

    private String entryChannel;

}
