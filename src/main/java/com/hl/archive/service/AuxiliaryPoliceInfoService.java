package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.AuxiliaryPoliceInfoQueryDTO;
import com.hl.archive.domain.dto.PoliceStatisticsDTO;
import com.hl.archive.domain.dto.AuxiliaryPoliceStatisticsDTO;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.utils.SsoCacheUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.AuxiliaryPoliceInfoMapper;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
@Service
@RequiredArgsConstructor
public class AuxiliaryPoliceInfoService extends ServiceImpl<AuxiliaryPoliceInfoMapper, AuxiliaryPoliceInfo> {

    private final AuxiliaryPoliceInfoMapper auxiliaryPoliceInfoMapper;

    public Page<AuxiliaryPoliceInfo> pageList(AuxiliaryPoliceInfoQueryDTO dto) {
        LambdaQueryWrapper<AuxiliaryPoliceInfo> queryWrapper = Wrappers.<AuxiliaryPoliceInfo>lambdaQuery();
        if (StrUtil.isNotBlank(dto.getOrganizationId())) {
            if (!"320412000000".equals(dto.getOrganizationId())) {
                String organizationName = SsoCacheUtil.getOrganizationName(dto.getOrganizationId());
                organizationName = organizationName.replaceAll("派出所", "")
                        .replaceAll("武进分局", "");
                queryWrapper.like(AuxiliaryPoliceInfo::getOrganization, organizationName);
            }
        }

        if (StrUtil.isNotBlank(dto.getQuery())){
            queryWrapper.and(w-> w.like(AuxiliaryPoliceInfo::getName, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getIdCard, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getOrganization, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getEmployeeNumber, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getRegisteredAddress, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getResidenceAddress, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getNativePlace, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getPosition, dto.getQuery()));
        }

        if (StrUtil.isNotBlank(dto.getEmploymentStatus())){
            queryWrapper.eq(AuxiliaryPoliceInfo::getEmploymentStatus, dto.getEmploymentStatus());
        }
        if (StrUtil.isNotBlank(dto.getEducationLevel())){
            queryWrapper.eq(AuxiliaryPoliceInfo::getEducationLevel, dto.getEducationLevel());
        }
        if (StrUtil.isNotBlank(dto.getMaritalStatus())){
            queryWrapper.eq(AuxiliaryPoliceInfo::getMaritalStatus, dto.getMaritalStatus());
        }

        // 离职排在最后面 employmentStatus 字段 内容 在职 离职
        queryWrapper.orderByAsc(AuxiliaryPoliceInfo::getEmploymentStatus);


        Page<AuxiliaryPoliceInfo> auxiliaryPoliceInfoPage = page(Page.of(dto.getPage(), dto.getLimit()), queryWrapper);
        return auxiliaryPoliceInfoPage;
    }

    public void exportAuxiliaryPoliceInfo(AuxiliaryPoliceInfoQueryDTO dto, HttpServletResponse response) throws IOException {
        dto.setLimit(Integer.MAX_VALUE);
        Page<AuxiliaryPoliceInfo> auxiliaryPoliceInfoPage = this.pageList(dto);
        List<AuxiliaryPoliceInfo> records = auxiliaryPoliceInfoPage.getRecords();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        FastExcel.write(response.getOutputStream(), AuxiliaryPoliceInfo.class)
                .autoCloseStream(Boolean.FALSE).sheet("辅警信息导出")
                .doWrite(records);
    }


    public AuxiliaryPoliceStatisticsDTO getAuxiliaryPoliceStatisticsByOrgId(StatisticsQueryRequest request) {
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (!"320412000000".equals(request.getOrganizationId())) {
                // 非320412000000时，截取前8位
                request.setOrganizationId(request.getOrganizationId().substring(0, 8));
            }
            // 320412000000时保持原值，在SQL中单独处理
        }
        AuxiliaryPoliceStatisticsDTO auxiliaryPoliceStatisticsByOrgId = auxiliaryPoliceInfoMapper.getAuxiliaryPoliceStatisticsByOrgId(request);

        String department = auxiliaryPoliceStatisticsByOrgId.getDepartment();
        if (StrUtil.isNotBlank(department)) {
            String organizationName = SsoCacheUtil.getOrganizationName(department);
            auxiliaryPoliceStatisticsByOrgId.setDepartmentName(organizationName);
        }
        return auxiliaryPoliceStatisticsByOrgId;
    }

    public List<AuxiliaryPoliceStatisticsDTO> getAuxiliaryPoliceStatisticsByDepartment() {
        List<AuxiliaryPoliceStatisticsDTO> auxiliaryPoliceStatisticsByDepartment = auxiliaryPoliceInfoMapper.getAuxiliaryPoliceStatisticsByDepartment();

        for (AuxiliaryPoliceStatisticsDTO auxiliaryPoliceStatisticsDTO : auxiliaryPoliceStatisticsByDepartment) {
            String department = auxiliaryPoliceStatisticsDTO.getDepartment();
            if (StrUtil.isNotBlank(department)) {
                String organizationName = SsoCacheUtil.getOrganizationName(department);
                auxiliaryPoliceStatisticsDTO.setDepartmentName(organizationName);
            }
        }

        return auxiliaryPoliceStatisticsByDepartment;
    }

    /**
     * 辅警统计数字穿透查询
     */
    public Page<AuxiliaryPoliceInfo> getAuxiliaryPoliceListByStatisticsType(StatisticsDrillDownRequest request) {
        // 创建分页对象
        Page<AuxiliaryPoliceInfo> page = new Page<>(request.getPage(), request.getLimit());
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if (!"320412000000".equals(request.getOrganizationId())) {
                // 非320412000000时，截取前8位
                request.setOrganizationId(request.getOrganizationId().substring(0, 8));
            }
            // 320412000000时保持原值，在SQL中单独处理
        }
        // 执行分页查询
        Page<AuxiliaryPoliceInfo> resultPage = auxiliaryPoliceInfoMapper.getAuxiliaryPoliceListByStatisticsType(page, request);
        return resultPage;
    }




    public void cleanAuxiliaryPoliceInfo() {

        for (AuxiliaryPoliceInfo auxiliaryPoliceInfo : this.list()) {
            String organization = auxiliaryPoliceInfo.getOrganization();
            if (StrUtil.isNotBlank(organization)) {
               organization = organization.replaceAll("派出所", "")
                        .replaceAll("武进分局", "")
                       .replaceAll("常州市公安局","");
                String organizationIdByNameWithLike = SsoCacheUtil.getOrganizationIdByNameWithLike(organization);
                auxiliaryPoliceInfo.setOrganizationId(organizationIdByNameWithLike);
                this.updateById(auxiliaryPoliceInfo);
            }
        }
    }
}
