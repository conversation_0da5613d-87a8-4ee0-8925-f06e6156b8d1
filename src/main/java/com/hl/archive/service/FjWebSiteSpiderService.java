package com.hl.archive.service;

import com.hl.archive.domain.dto.WjPoliceNewsDto;
import com.hl.archive.domain.dto.WjPoliceSpiderResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 武进公安信息网爬虫服务
 */
@Slf4j
@Service
public class FjWebSiteSpiderService {

    private final OkHttpClient httpClient;
    private static final String BASE_URL = "http://50.56.116.11/ArticleList.asp?mode=%ED%C2%BE%AF%D1%A7%CC%C3";
    private static final Pattern DATE_PATTERN = Pattern.compile("\\[(\\d{2})/(\\d{1,2})/(\\d{1,2})\\]");
    private static final Pattern ID_PATTERN = Pattern.compile("id=(\\d+)");
    private static final Pattern PAGE_INFO_PATTERN = Pattern.compile("总计(\\d+)条.*共有(\\d+)/(\\d+)页");

    public FjWebSiteSpiderService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 爬取武进公安信息网数据
     * @param baseUrl 基础URL（如果为空则使用默认URL）
     * @return 爬虫结果
     */
    public WjPoliceSpiderResult crawlWjPoliceNews(String baseUrl) {
        WjPoliceSpiderResult result = new WjPoliceSpiderResult();
        result.setSuccess(false);

        try {
            String url = (baseUrl != null && !baseUrl.trim().isEmpty()) ? baseUrl : BASE_URL;

            // 首先获取第一页来获取总页数信息
            WjPoliceSpiderResult firstPageResult = crawlSinglePage(url, 1);
            if (!firstPageResult.getSuccess()) {
                return firstPageResult;
            }

            result.setTotalPages(firstPageResult.getTotalPages());
            result.setTotalRecords(firstPageResult.getTotalRecords());
            result.setCurrentPage(1);

            // 爬取所有页面的数据
            List<WjPoliceNewsDto> allNews = new ArrayList<>();
            WjPoliceNewsDto eighthDengfengNews = null;

            for (int page = 1; page <= firstPageResult.getTotalPages(); page++) {
                log.info("正在爬取第{}页数据...", page);
                WjPoliceSpiderResult pageResult = crawlSinglePage(url, page);

                if (pageResult.getSuccess() && pageResult.getNewsList() != null) {
                    allNews.addAll(pageResult.getNewsList());

                    // 查找第八次"登锋"砺警学堂
                    if (eighthDengfengNews == null) {
                        eighthDengfengNews = findEighthDengfengNews(pageResult.getNewsList());
                    }
                }
            }

            result.setNewsList(allNews);
            result.setEighthDengfengNews(eighthDengfengNews);
            result.setSuccess(true);

            log.info("爬取完成，共获取{}条新闻数据", allNews.size());

        } catch (Exception e) {
            log.error("爬取武进公安信息网数据失败", e);
            result.setErrorMessage("爬取失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 爬取单页数据
     * @param baseUrl 基础URL
     * @param page 页码
     * @return 单页爬虫结果
     */
    private WjPoliceSpiderResult crawlSinglePage(String baseUrl, int page) {
        WjPoliceSpiderResult result = new WjPoliceSpiderResult();
        result.setSuccess(false);
        result.setCurrentPage(page);

        try {
            String url = baseUrl + "&page=" + page;
            log.debug("正在请求URL: {}", url);

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    result.setErrorMessage("HTTP请求失败，状态码: " + response.code());
                    return result;
                }

                String html = response.body().string();
                Document doc = Jsoup.parse(html, "GBK");

                // 解析页面信息
                parsePageInfo(doc, result);

                // 解析新闻列表
                List<WjPoliceNewsDto> newsList = parseNewsList(doc, page);
                result.setNewsList(newsList);
                result.setSuccess(true);

                log.debug("第{}页解析完成，获取{}条新闻", page, newsList.size());
            }

        } catch (IOException e) {
            log.error("请求第{}页失败", page, e);
            result.setErrorMessage("网络请求失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("解析第{}页失败", page, e);
            result.setErrorMessage("页面解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析页面信息（总页数、总记录数等）
     */
    private void parsePageInfo(Document doc, WjPoliceSpiderResult result) {
        try {
            // 查找包含页面信息的文本，例如："总计38条　共有3/3页"
            Elements pageInfoElements = doc.select("td.t11");
            for (Element element : pageInfoElements) {
                String text = element.text();
                Matcher matcher = PAGE_INFO_PATTERN.matcher(text);
                if (matcher.find()) {
                    result.setTotalRecords(Integer.parseInt(matcher.group(1)));
                    result.setTotalPages(Integer.parseInt(matcher.group(3)));
                    log.debug("解析到页面信息: 总记录数={}, 总页数={}", result.getTotalRecords(), result.getTotalPages());
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("解析页面信息失败", e);
        }
    }

    /**
     * 解析新闻列表
     */
    private List<WjPoliceNewsDto> parseNewsList(Document doc, int page) {
        List<WjPoliceNewsDto> newsList = new ArrayList<>();

        try {
            // 查找新闻条目，根据HTML结构定位
            Elements newsRows = doc.select("tr[valign=middle]");

            for (Element row : newsRows) {
                try {
                    WjPoliceNewsDto news = parseNewsItem(row, page);
                    if (news != null) {
                        newsList.add(news);
                    }
                } catch (Exception e) {
                    log.warn("解析单条新闻失败", e);
                }
            }

        } catch (Exception e) {
            log.error("解析新闻列表失败", e);
        }

        return newsList;
    }

    /**
     * 解析单条新闻
     */
    private WjPoliceNewsDto parseNewsItem(Element row, int page) {
        try {
            Elements linkElements = row.select("a[href]");
            if (linkElements.isEmpty()) {
                return null;
            }

            Element linkElement = linkElements.first();
            String title = linkElement.text().trim();
            String href = linkElement.attr("href");

            // 跳过空标题
            if (title.isEmpty()) {
                return null;
            }

            WjPoliceNewsDto news = new WjPoliceNewsDto();
            news.setTitle(title);
            news.setUrl(href);
            news.setPageNumber(page);

            // 检查是否包含"登锋"关键字
            news.setContainsDengfeng(title.contains("登锋"));

            // 提取新闻ID
            Matcher idMatcher = ID_PATTERN.matcher(href);
            if (idMatcher.find()) {
                news.setNewsId(idMatcher.group(1));
            }

            // 解析日期
            Elements dateElements = row.select("span.t2");
            if (!dateElements.isEmpty()) {
                String dateText = dateElements.first().text();
                LocalDate publishDate = parseDate(dateText);
                news.setPublishDate(publishDate);
            }

            return news;

        } catch (Exception e) {
            log.warn("解析新闻条目失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析日期字符串
     * @param dateText 日期文本，格式如 [24/4/7]
     * @return LocalDate对象
     */
    private LocalDate parseDate(String dateText) {
        try {
            Matcher matcher = DATE_PATTERN.matcher(dateText);
            if (matcher.find()) {
                int year = 2000 + Integer.parseInt(matcher.group(1)); // 24 -> 2024
                int month = Integer.parseInt(matcher.group(2));
                int day = Integer.parseInt(matcher.group(3));
                return LocalDate.of(year, month, day);
            }
        } catch (Exception e) {
            log.warn("解析日期失败: {}", dateText, e);
        }
        return null;
    }

    /**
     * 查找第八次"登锋"砺警学堂
     */
    private WjPoliceNewsDto findEighthDengfengNews(List<WjPoliceNewsDto> newsList) {
        for (WjPoliceNewsDto news : newsList) {
            if (news.getTitle() != null &&
                news.getTitle().contains("第八次") &&
                news.getTitle().contains("登锋") &&
                news.getTitle().contains("砺警学堂")) {
                log.info("找到第八次登锋砺警学堂: {}", news.getTitle());
                return news;
            }
        }
        return null;
    }

    /**
     * 只爬取指定页面的数据
     * @param baseUrl 基础URL
     * @param page 页码
     * @return 爬虫结果
     */
    public WjPoliceSpiderResult crawlSinglePageOnly(String baseUrl, int page) {
        String url = (baseUrl != null && !baseUrl.trim().isEmpty()) ? baseUrl : BASE_URL;
        return crawlSinglePage(url, page);
    }
}
