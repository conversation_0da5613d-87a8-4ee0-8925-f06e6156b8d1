package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.domain.dto.PoliceStatisticsDTO;
import com.hl.archive.domain.dto.AuxiliaryPoliceStatisticsDTO;
import com.hl.archive.domain.request.StatisticsQueryRequest;
import com.hl.archive.domain.request.StatisticsDrillDownRequest;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuxiliaryPoliceInfoMapper extends BaseMapper<AuxiliaryPoliceInfo> {
    List<AuxiliaryPoliceStatisticsDTO> getAuxiliaryPoliceStatisticsByDepartment();

    AuxiliaryPoliceStatisticsDTO getAuxiliaryPoliceStatisticsByOrgId(StatisticsQueryRequest request);

    /**
     * 辅警统计数字穿透查询 - 分页查询辅警基本信息
     */
    Page<AuxiliaryPoliceInfo> getAuxiliaryPoliceListByStatisticsType(@Param("page") Page<AuxiliaryPoliceInfo> page,
                                                                     @Param("request") StatisticsDrillDownRequest request);
}