package com.hl.archive.demo;

import com.hl.archive.domain.dto.WjPoliceNewsDto;
import com.hl.archive.domain.dto.WjPoliceSpiderResult;
import com.hl.archive.service.FjWebSiteSpiderService;
import lombok.extern.slf4j.Slf4j;

/**
 * 武进公安信息网爬虫演示类
 * 
 * 这个类演示了如何使用爬虫服务来获取武进公安信息网的数据
 */
@Slf4j
public class WjPoliceSpiderDemo {

    public static void main(String[] args) {
        // 创建爬虫服务实例
        FjWebSiteSpiderService spiderService = new FjWebSiteSpiderService();
        
        log.info("=== 武进公安信息网爬虫演示 ===");
        
        // 演示1：爬取第一页数据
        demonstrateSinglePageCrawl(spiderService);
        
        // 演示2：查找第八次登锋砺警学堂（注意：这会爬取所有页面）
        // demonstrateEighthDengfengSearch(spiderService);
    }
    
    /**
     * 演示单页爬取功能
     */
    private static void demonstrateSinglePageCrawl(FjWebSiteSpiderService spiderService) {
        log.info("\n--- 演示：爬取第一页数据 ---");
        
        try {
            WjPoliceSpiderResult result = spiderService.crawlSinglePageOnly(null, 1);
            
            if (result.getSuccess()) {
                log.info("爬取成功！");
                log.info("总页数: {}", result.getTotalPages());
                log.info("总记录数: {}", result.getTotalRecords());
                log.info("当前页: {}", result.getCurrentPage());
                log.info("当前页新闻数量: {}", result.getNewsList().size());
                
                log.info("\n新闻列表:");
                for (int i = 0; i < Math.min(5, result.getNewsList().size()); i++) {
                    WjPoliceNewsDto news = result.getNewsList().get(i);
                    log.info("{}. {}", i + 1, news.getTitle());
                    log.info("   链接: {}", news.getUrl());
                    log.info("   时间: {}", news.getPublishDate());
                    log.info("   包含'登锋': {}", news.getContainsDengfeng());
                    log.info("   新闻ID: {}", news.getNewsId());
                    log.info("");
                }
                
                // 统计包含"登锋"的新闻
                long dengfengCount = result.getNewsList().stream()
                        .filter(WjPoliceNewsDto::getContainsDengfeng)
                        .count();
                log.info("第一页中包含'登锋'关键字的新闻: {} 条", dengfengCount);
                
            } else {
                log.error("爬取失败: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("演示过程中发生异常", e);
        }
    }
    
    /**
     * 演示查找第八次登锋砺警学堂功能
     * 注意：这个方法会爬取所有页面，可能需要较长时间
     */
    private static void demonstrateEighthDengfengSearch(FjWebSiteSpiderService spiderService) {
        log.info("\n--- 演示：查找第八次登锋砺警学堂 ---");
        log.info("注意：这将爬取所有页面，可能需要一些时间...");
        
        try {
            WjPoliceSpiderResult result = spiderService.crawlWjPoliceNews(null);
            
            if (result.getSuccess()) {
                log.info("全量爬取成功！");
                log.info("总页数: {}", result.getTotalPages());
                log.info("总新闻数: {}", result.getNewsList().size());
                
                if (result.getEighthDengfengNews() != null) {
                    WjPoliceNewsDto eighthNews = result.getEighthDengfengNews();
                    log.info("\n找到第八次登锋砺警学堂:");
                    log.info("标题: {}", eighthNews.getTitle());
                    log.info("链接: {}", eighthNews.getUrl());
                    log.info("时间: {}", eighthNews.getPublishDate());
                    log.info("新闻ID: {}", eighthNews.getNewsId());
                } else {
                    log.warn("未找到第八次登锋砺警学堂信息");
                }
                
                // 统计所有包含"登锋"的新闻
                long totalDengfengCount = result.getNewsList().stream()
                        .filter(WjPoliceNewsDto::getContainsDengfeng)
                        .count();
                log.info("总共包含'登锋'关键字的新闻: {} 条", totalDengfengCount);
                
            } else {
                log.error("全量爬取失败: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("演示过程中发生异常", e);
        }
    }
    
    /**
     * 演示如何使用自定义URL
     */
    private static void demonstrateCustomUrl(FjWebSiteSpiderService spiderService) {
        log.info("\n--- 演示：使用自定义URL ---");
        
        String customUrl = "http://50.56.116.11/ArticleList.asp?mode=%ED%C2%BE%AF%D1%A7%CC%C3";
        
        try {
            WjPoliceSpiderResult result = spiderService.crawlSinglePageOnly(customUrl, 1);
            
            if (result.getSuccess()) {
                log.info("使用自定义URL爬取成功！");
                log.info("获取到 {} 条新闻", result.getNewsList().size());
            } else {
                log.error("使用自定义URL爬取失败: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("演示过程中发生异常", e);
        }
    }
}
