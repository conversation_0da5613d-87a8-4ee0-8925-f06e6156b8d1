package com.hl.archive.service;

import com.hl.archive.domain.dto.WjPoliceNewsDto;
import com.hl.archive.domain.dto.WjPoliceSpiderResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 武进公安信息网爬虫服务测试
 */
@Slf4j
@SpringBootTest
public class FjWebSiteSpiderServiceTest {

    @Test
    public void testParseLocalHtml() {
        try {
            // 读取本地HTML文件进行测试
            String htmlContent = Files.readString(Paths.get("docs/武进公安信息网.html"));
            
            FjWebSiteSpiderService spiderService = new FjWebSiteSpiderService();
            
            // 这里我们需要创建一个测试方法来解析本地HTML
            // 由于原方法是私有的，我们可以创建一个公共的测试方法
            
            log.info("HTML文件读取成功，长度: {}", htmlContent.length());
            
            // 可以在这里添加更多的测试逻辑
            
        } catch (IOException e) {
            log.error("读取HTML文件失败", e);
        }
    }

    @Test
    public void testCrawlSinglePage() {
        FjWebSiteSpiderService spiderService = new FjWebSiteSpiderService();
        
        // 测试爬取第一页
        WjPoliceSpiderResult result = spiderService.crawlSinglePageOnly(null, 1);
        
        log.info("爬取结果: success={}, totalPages={}, totalRecords={}", 
                result.getSuccess(), result.getTotalPages(), result.getTotalRecords());
        
        if (result.getSuccess() && result.getNewsList() != null) {
            log.info("获取到{}条新闻", result.getNewsList().size());
            
            for (WjPoliceNewsDto news : result.getNewsList()) {
                log.info("新闻: {} - {} - {}", news.getTitle(), news.getPublishDate(), news.getUrl());
                
                if (news.getContainsDengfeng()) {
                    log.info("包含'登锋'关键字的新闻: {}", news.getTitle());
                }
            }
        } else {
            log.error("爬取失败: {}", result.getErrorMessage());
        }
    }

    @Test
    public void testFindEighthDengfeng() {
        FjWebSiteSpiderService spiderService = new FjWebSiteSpiderService();
        
        // 测试查找第八次登锋砺警学堂
        WjPoliceSpiderResult result = spiderService.crawlWjPoliceNews(null);
        
        if (result.getSuccess()) {
            log.info("爬取成功，总共{}页，{}条新闻", result.getTotalPages(), result.getNewsList().size());
            
            if (result.getEighthDengfengNews() != null) {
                WjPoliceNewsDto eighthNews = result.getEighthDengfengNews();
                log.info("找到第八次登锋砺警学堂:");
                log.info("标题: {}", eighthNews.getTitle());
                log.info("链接: {}", eighthNews.getUrl());
                log.info("时间: {}", eighthNews.getPublishDate());
                log.info("新闻ID: {}", eighthNews.getNewsId());
            } else {
                log.warn("未找到第八次登锋砺警学堂");
            }
            
            // 统计包含"登锋"的新闻数量
            long dengfengCount = result.getNewsList().stream()
                    .filter(WjPoliceNewsDto::getContainsDengfeng)
                    .count();
            log.info("包含'登锋'关键字的新闻共{}条", dengfengCount);
            
        } else {
            log.error("爬取失败: {}", result.getErrorMessage());
        }
    }
}
