# 武进公安信息网爬虫使用说明

## 概述

本爬虫用于爬取武进公安信息网（http://************）的砺警学堂数据，特别是获取"登锋"砺警学堂相关的新闻信息。

## 功能特性

1. **自动获取总页数**：爬虫会自动解析页面获取总页数信息
2. **全量数据爬取**：支持爬取所有页面的数据
3. **单页数据爬取**：支持爬取指定页面的数据
4. **智能解析**：自动解析新闻标题、链接、发布时间等信息
5. **关键字识别**：自动识别包含"登锋"关键字的新闻
6. **特定新闻查找**：专门查找第八次"登锋"砺警学堂信息

## 技术实现

### 依赖库
- **Jsoup 1.17.2**：用于HTML解析
- **OkHttp 4.12.0**：用于HTTP请求
- **Spring Boot**：框架支持

### 核心类

1. **FjWebSiteSpiderService**：爬虫核心服务类
2. **WjPoliceNewsDto**：新闻数据传输对象
3. **WjPoliceSpiderResult**：爬虫结果封装类
4. **WjPoliceSpiderController**：REST API控制器

## API接口

### 1. 爬取所有页面数据
```
POST /api/spider/wj-police/crawl-all
参数：
- baseUrl (可选): 基础URL，为空时使用默认URL
```

### 2. 爬取指定页面数据
```
POST /api/spider/wj-police/crawl-page
参数：
- baseUrl (可选): 基础URL，为空时使用默认URL
- page (默认1): 页码
```

### 3. 获取第八次"登锋"砺警学堂信息
```
GET /api/spider/wj-police/eighth-dengfeng
参数：
- baseUrl (可选): 基础URL，为空时使用默认URL
```

## 数据结构

### WjPoliceNewsDto
```json
{
  "title": "新闻标题",
  "url": "新闻链接",
  "publishDate": "2024-04-07",
  "pageNumber": 1,
  "containsDengfeng": true,
  "newsId": "66028"
}
```

### WjPoliceSpiderResult
```json
{
  "totalPages": 3,
  "currentPage": 1,
  "totalRecords": 38,
  "newsList": [...],
  "success": true,
  "errorMessage": null,
  "eighthDengfengNews": {...}
}
```

## 使用示例

### 1. 爬取所有数据
```bash
curl -X POST "http://localhost:8080/api/spider/wj-police/crawl-all"
```

### 2. 爬取第3页数据
```bash
curl -X POST "http://localhost:8080/api/spider/wj-police/crawl-page?page=3"
```

### 3. 查找第八次登锋砺警学堂
```bash
curl -X GET "http://localhost:8080/api/spider/wj-police/eighth-dengfeng"
```

## 配置说明

### 默认配置
- 基础URL：`http://************/ArticleList.asp?mode=%ED%C2%BE%AF%D1%A7%CC%C3`
- 连接超时：30秒
- 读取超时：30秒
- 编码格式：GBK

### 自定义配置
可以通过传入baseUrl参数来使用自定义的URL。

## 注意事项

1. **网络环境**：确保能够访问武进公安信息网
2. **请求频率**：建议控制请求频率，避免对目标网站造成压力
3. **数据时效性**：爬取的数据可能会随着网站更新而变化
4. **错误处理**：爬虫包含完善的错误处理机制，会返回详细的错误信息

## 测试

项目包含测试类 `FjWebSiteSpiderServiceTest`，可以运行测试来验证爬虫功能：

```bash
mvn test -Dtest=FjWebSiteSpiderServiceTest
```

## 日志

爬虫运行过程中会输出详细的日志信息，包括：
- 请求URL
- 解析进度
- 数据统计
- 错误信息

## 扩展性

爬虫设计具有良好的扩展性，可以轻松添加：
- 新的数据字段解析
- 不同的过滤条件
- 数据存储功能
- 定时任务支持
