package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWgwjdjb;
import com.hl.orasync.domain.VWjWgwjdjbToPoliceViolationSummaryMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__468;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__468.class,
    uses = {ConversionUtils.class,VWjWgwjdjbToPoliceViolationSummaryMapper.class},
    imports = {}
)
public interface PoliceViolationSummaryToVWjWgwjdjbMapper extends BaseMapper<PoliceViolationSummary, VWjWgwjdjb> {
  @Mapping(
      target = "ajbh",
      source = "caseNo"
  )
  @Mapping(
      target = "slsj",
      source = "acceptDate"
  )
  @Mapping(
      target = "djdwmc",
      source = "reportOrg"
  )
  @Mapping(
      target = "lzscdw",
      source = "detentionOrg"
  )
  @Mapping(
      target = "lzscsj",
      source = "detentionDate"
  )
  @Mapping(
      target = "yjsj",
      source = "transferDate"
  )
  @Mapping(
      target = "chqk",
      source = "preliminaryResult"
  )
  @Mapping(
      target = "chsj",
      source = "preliminaryStart"
  )
  @Mapping(
      target = "hssj",
      source = "meetingDate"
  )
  @Mapping(
      target = "fxdw",
      source = "foundOrg"
  )
  @Mapping(
      target = "chdw",
      source = "preliminaryOrg"
  )
  @Mapping(
      target = "sldw",
      source = "acceptOrg"
  )
  @Mapping(
      target = "fxsj",
      source = "foundDate"
  )
  @Mapping(
      target = "yjdw",
      source = "transferOrg"
  )
  @Mapping(
      target = "wjwfss",
      source = "violationFact"
  )
  @Mapping(
      target = "wjwflxmc",
      source = "violationType"
  )
  @Mapping(
      target = "hsdw",
      source = "meetingOrg"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "wtxslymc",
      source = "clueSource"
  )
  @Mapping(
      target = "wtxsnr",
      source = "clueContent"
  )
  @Mapping(
      target = "dcqk",
      source = "investigationResult"
  )
  @Mapping(
      target = "dcsj",
      source = "investigationStart"
  )
  @Mapping(
      target = "hsjg",
      source = "meetingResult"
  )
  @Mapping(
      target = "ladw",
      source = "caseOrg"
  )
  @Mapping(
      target = "dcdw",
      source = "investigationOrg"
  )
  @Mapping(
      target = "lasj",
      source = "caseDate"
  )
  VWjWgwjdjb convert(PoliceViolationSummary source);

  @Mapping(
      target = "ajbh",
      source = "caseNo"
  )
  @Mapping(
      target = "slsj",
      source = "acceptDate"
  )
  @Mapping(
      target = "djdwmc",
      source = "reportOrg"
  )
  @Mapping(
      target = "lzscdw",
      source = "detentionOrg"
  )
  @Mapping(
      target = "lzscsj",
      source = "detentionDate"
  )
  @Mapping(
      target = "yjsj",
      source = "transferDate"
  )
  @Mapping(
      target = "chqk",
      source = "preliminaryResult"
  )
  @Mapping(
      target = "chsj",
      source = "preliminaryStart"
  )
  @Mapping(
      target = "hssj",
      source = "meetingDate"
  )
  @Mapping(
      target = "fxdw",
      source = "foundOrg"
  )
  @Mapping(
      target = "chdw",
      source = "preliminaryOrg"
  )
  @Mapping(
      target = "sldw",
      source = "acceptOrg"
  )
  @Mapping(
      target = "fxsj",
      source = "foundDate"
  )
  @Mapping(
      target = "yjdw",
      source = "transferOrg"
  )
  @Mapping(
      target = "wjwfss",
      source = "violationFact"
  )
  @Mapping(
      target = "wjwflxmc",
      source = "violationType"
  )
  @Mapping(
      target = "hsdw",
      source = "meetingOrg"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "wtxslymc",
      source = "clueSource"
  )
  @Mapping(
      target = "wtxsnr",
      source = "clueContent"
  )
  @Mapping(
      target = "dcqk",
      source = "investigationResult"
  )
  @Mapping(
      target = "dcsj",
      source = "investigationStart"
  )
  @Mapping(
      target = "hsjg",
      source = "meetingResult"
  )
  @Mapping(
      target = "ladw",
      source = "caseOrg"
  )
  @Mapping(
      target = "dcdw",
      source = "investigationOrg"
  )
  @Mapping(
      target = "lasj",
      source = "caseDate"
  )
  VWjWgwjdjb convert(PoliceViolationSummary source, @MappingTarget VWjWgwjdjb target);
}
