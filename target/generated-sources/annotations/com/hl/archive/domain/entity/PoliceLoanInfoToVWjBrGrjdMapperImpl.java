package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrGrjd;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-08T09:13:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceLoanInfoToVWjBrGrjdMapperImpl implements PoliceLoanInfoToVWjBrGrjdMapper {

    @Override
    public VWjBrGrjd convert(PoliceLoanInfo source) {
        if ( source == null ) {
            return null;
        }

        VWjBrGrjd vWjBrGrjd = new VWjBrGrjd();

        vWjBrGrjd.setJdyt( source.getLoanPurpose() );
        vWjBrGrjd.setGmsfhm( source.getIdCard() );
        if ( source.getLoanDate() != null ) {
            vWjBrGrjd.setJdrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getLoanDate() ) );
        }
        vWjBrGrjd.setJdlxmc( source.getLoanInfo() );
        if ( source.getRepaymentDeadline() != null ) {
            vWjBrGrjd.setHkqx( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRepaymentDeadline() ) );
        }
        if ( source.getLoanAmount() != null ) {
            vWjBrGrjd.setJe( source.getLoanAmount().toString() );
        }
        vWjBrGrjd.setXmJddx( source.getLenderName() );

        return vWjBrGrjd;
    }

    @Override
    public VWjBrGrjd convert(PoliceLoanInfo source, VWjBrGrjd target) {
        if ( source == null ) {
            return target;
        }

        target.setJdyt( source.getLoanPurpose() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getLoanDate() != null ) {
            target.setJdrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getLoanDate() ) );
        }
        else {
            target.setJdrq( null );
        }
        target.setJdlxmc( source.getLoanInfo() );
        if ( source.getRepaymentDeadline() != null ) {
            target.setHkqx( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRepaymentDeadline() ) );
        }
        else {
            target.setHkqx( null );
        }
        if ( source.getLoanAmount() != null ) {
            target.setJe( source.getLoanAmount().toString() );
        }
        else {
            target.setJe( null );
        }
        target.setXmJddx( source.getLenderName() );

        return target;
    }
}
