package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnGwth;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-08T09:13:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceChildrenForeignMarriageToVWjZnGwthMapperImpl implements PoliceChildrenForeignMarriageToVWjZnGwthMapper {

    @Override
    public VWjZnGwth convert(PoliceChildrenForeignMarriage source) {
        if ( source == null ) {
            return null;
        }

        VWjZnGwth vWjZnGwth = new VWjZnGwth();

        vWjZnGwth.setXmZn( source.getChildName() );
        vWjZnGwth.setGzdwZnpo( source.getWorkStudyUnit() );
        vWjZnGwth.setGjZnpo( source.getSpouseCountry() );
        vWjZnGwth.setZwZnpo( source.getPosition() );
        vWjZnGwth.setGmsfhm( source.getIdCard() );
        vWjZnGwth.setXmZnpo( source.getSpouseName() );
        if ( source.getRegistrationDate() != null ) {
            vWjZnGwth.setDjrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegistrationDate() ) );
        }

        return vWjZnGwth;
    }

    @Override
    public VWjZnGwth convert(PoliceChildrenForeignMarriage source, VWjZnGwth target) {
        if ( source == null ) {
            return target;
        }

        target.setXmZn( source.getChildName() );
        target.setGzdwZnpo( source.getWorkStudyUnit() );
        target.setGjZnpo( source.getSpouseCountry() );
        target.setZwZnpo( source.getPosition() );
        target.setGmsfhm( source.getIdCard() );
        target.setXmZnpo( source.getSpouseName() );
        if ( source.getRegistrationDate() != null ) {
            target.setDjrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegistrationDate() ) );
        }
        else {
            target.setDjrq( null );
        }

        return target;
    }
}
