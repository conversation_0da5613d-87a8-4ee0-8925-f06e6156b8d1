package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceViolationPerson;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-08T09:13:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjWgwjdjbRyclToPoliceViolationPersonMapperImpl implements VWjWgwjdjbRyclToPoliceViolationPersonMapper {

    @Override
    public PoliceViolationPerson convert(VWjWgwjdjbRycl source) {
        if ( source == null ) {
            return null;
        }

        PoliceViolationPerson policeViolationPerson = new PoliceViolationPerson();

        policeViolationPerson.setGender( source.getXbmc() );
        policeViolationPerson.setIdCard( source.getGmsfhm() );
        policeViolationPerson.setPoliticalStatus( source.getZzmmmc() );
        policeViolationPerson.setCaseOrg( source.getDwmc() );
        policeViolationPerson.setIsAccountability( source.getSfdcwzmc() );
        policeViolationPerson.setRemark( source.getBz() );
        policeViolationPerson.setBirthDate( ConversionUtils.strToLocalDate( source.getCsrq() ) );
        policeViolationPerson.setWtXxzjbh( source.getWtXxzjbh() );
        policeViolationPerson.setXxzjbh( source.getXxzjbh() );
        policeViolationPerson.setJoinPartyDate( ConversionUtils.strToLocalDate( source.getRdrq() ) );
        policeViolationPerson.setName( source.getXm() );
        policeViolationPerson.setRank( source.getZjmc() );
        policeViolationPerson.setPoliceDept( source.getJzbmmc() );
        policeViolationPerson.setDispositionCategory( source.getClxtmc() );
        policeViolationPerson.setPosition( source.getZwmc() );

        return policeViolationPerson;
    }

    @Override
    public PoliceViolationPerson convert(VWjWgwjdjbRycl source, PoliceViolationPerson target) {
        if ( source == null ) {
            return target;
        }

        target.setGender( source.getXbmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setPoliticalStatus( source.getZzmmmc() );
        target.setCaseOrg( source.getDwmc() );
        target.setIsAccountability( source.getSfdcwzmc() );
        target.setRemark( source.getBz() );
        target.setBirthDate( ConversionUtils.strToLocalDate( source.getCsrq() ) );
        target.setWtXxzjbh( source.getWtXxzjbh() );
        target.setXxzjbh( source.getXxzjbh() );
        target.setJoinPartyDate( ConversionUtils.strToLocalDate( source.getRdrq() ) );
        target.setName( source.getXm() );
        target.setRank( source.getZjmc() );
        target.setPoliceDept( source.getJzbmmc() );
        target.setDispositionCategory( source.getClxtmc() );
        target.setPosition( source.getZwmc() );

        return target;
    }
}
