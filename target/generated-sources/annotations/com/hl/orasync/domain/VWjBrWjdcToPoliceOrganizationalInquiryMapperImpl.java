package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOrganizationalInquiry;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-08T09:13:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrWjdcToPoliceOrganizationalInquiryMapperImpl implements VWjBrWjdcToPoliceOrganizationalInquiryMapper {

    @Override
    public PoliceOrganizationalInquiry convert(VWjBrWjdc source) {
        if ( source == null ) {
            return null;
        }

        PoliceOrganizationalInquiry policeOrganizationalInquiry = new PoliceOrganizationalInquiry();

        policeOrganizationalInquiry.setHandlingAuthority( source.getDcjg() );
        policeOrganizationalInquiry.setSuspectedViolations( source.getWfqk() );
        policeOrganizationalInquiry.setIdCard( source.getGmsfhm() );
        policeOrganizationalInquiry.setHandlingDate( ConversionUtils.strToDate( source.getDcrq() ) );

        return policeOrganizationalInquiry;
    }

    @Override
    public PoliceOrganizationalInquiry convert(VWjBrWjdc source, PoliceOrganizationalInquiry target) {
        if ( source == null ) {
            return target;
        }

        target.setHandlingAuthority( source.getDcjg() );
        target.setSuspectedViolations( source.getWfqk() );
        target.setIdCard( source.getGmsfhm() );
        target.setHandlingDate( ConversionUtils.strToDate( source.getDcrq() ) );

        return target;
    }
}
