package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceLoanInfo;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-08T09:13:30+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrGrjdToPoliceLoanInfoMapperImpl implements VWjBrGrjdToPoliceLoanInfoMapper {

    @Override
    public PoliceLoanInfo convert(VWjBrGrjd source) {
        if ( source == null ) {
            return null;
        }

        PoliceLoanInfo policeLoanInfo = new PoliceLoanInfo();

        policeLoanInfo.setIdCard( source.getGmsfhm() );
        policeLoanInfo.setLoanInfo( source.getJdlxmc() );
        policeLoanInfo.setLenderName( source.getXmJddx() );
        policeLoanInfo.setLoanDate( ConversionUtils.strToDate( source.getJdrq() ) );
        policeLoanInfo.setLoanPurpose( source.getJdyt() );
        policeLoanInfo.setRepaymentDeadline( ConversionUtils.strToDate( source.getHkqx() ) );
        policeLoanInfo.setLoanAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );

        return policeLoanInfo;
    }

    @Override
    public PoliceLoanInfo convert(VWjBrGrjd source, PoliceLoanInfo target) {
        if ( source == null ) {
            return target;
        }

        target.setIdCard( source.getGmsfhm() );
        target.setLoanInfo( source.getJdlxmc() );
        target.setLenderName( source.getXmJddx() );
        target.setLoanDate( ConversionUtils.strToDate( source.getJdrq() ) );
        target.setLoanPurpose( source.getJdyt() );
        target.setRepaymentDeadline( ConversionUtils.strToDate( source.getHkqx() ) );
        target.setLoanAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );

        return target;
    }
}
