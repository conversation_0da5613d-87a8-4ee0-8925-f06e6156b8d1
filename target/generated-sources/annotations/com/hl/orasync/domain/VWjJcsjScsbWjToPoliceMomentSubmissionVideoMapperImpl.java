package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-05T17:30:04+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjJcsjScsbWjToPoliceMomentSubmissionVideoMapperImpl implements VWjJcsjScsbWjToPoliceMomentSubmissionVideoMapper {

    @Override
    public PoliceMomentSubmissionVideo convert(VWjJcsjScsbWj source) {
        if ( source == null ) {
            return null;
        }

        PoliceMomentSubmissionVideo policeMomentSubmissionVideo = new PoliceMomentSubmissionVideo();

        policeMomentSubmissionVideo.setFileName( source.getWjmc() );
        policeMomentSubmissionVideo.setSbZjbh( source.getSbXxzjbh() );
        policeMomentSubmissionVideo.setFileUrl( source.getFjcl2() );
        policeMomentSubmissionVideo.setFileType( source.getWjlxmc() );
        policeMomentSubmissionVideo.setZjbh( source.getXxzjbh() );

        return policeMomentSubmissionVideo;
    }

    @Override
    public PoliceMomentSubmissionVideo convert(VWjJcsjScsbWj source, PoliceMomentSubmissionVideo target) {
        if ( source == null ) {
            return target;
        }

        target.setFileName( source.getWjmc() );
        target.setSbZjbh( source.getSbXxzjbh() );
        target.setFileUrl( source.getFjcl2() );
        target.setFileType( source.getWjlxmc() );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
