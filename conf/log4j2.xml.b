<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="60">
    <Properties>
        <Property name="LOG_HOME">logs</Property>
        <Property name="MAX_FILE_SIZE">50MB</Property>
        <Property name="MAX_ARCHIVE_FILES">30</Property>
        <Property name="CONSOLE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{%-5level}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=cyan, DEBUG=green bold, TRACE=blue}
                    %style{[%t]}{magenta} %style{%c{1.}}{cyan} %style{[%L]}{blue} - %highlight{%msg%n}{FATAL=red blink, ERROR=red, WARN=yellow}</Property>
        <Property name="FILE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %c{1.} - %msg%n</Property>
    </Properties>

    <Appenders>

        <!-- 同步 Console -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 同步文件 appenders -->
        <RollingFile name="InfoFile" fileName="${LOG_HOME}/info.log"
                     filePattern="${LOG_HOME}/info-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>

        <RollingFile name="WarnFile" fileName="${LOG_HOME}/warn.log"
                     filePattern="${LOG_HOME}/warn-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>

        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>

        <RollingFile name="SecretFile" fileName="${LOG_HOME}/secret.log"
                     filePattern="${LOG_HOME}/secret-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>

        <!-- 异步包装 -->
        <Async name="AsyncInfo">
            <AppenderRef ref="InfoFile"/>
        </Async>

        <Async name="AsyncWarn">
            <AppenderRef ref="WarnFile"/>
        </Async>

        <Async name="AsyncError">
            <AppenderRef ref="ErrorFile"/>
        </Async>

        <Async name="AsyncSecret">
            <AppenderRef ref="SecretFile"/>
        </Async>

    </Appenders>

    <Loggers>

        <!-- 单独输出 secret 包的日志 -->
        <Logger name="com.hl.traffic.listener" level="info" additivity="false">
            <AppenderRef ref="AsyncSecret"/>
        </Logger>

        <Logger name="com.hl.traffic.processor" level="info" additivity="false">
            <AppenderRef ref="AsyncSecret"/>
        </Logger>

<!--        不输出数据-->
<!--        <Logger name="com.example.noisy" level="off" additivity="false"/>-->

        <!-- Root 分级异步输出 -->
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AsyncInfo"/>
            <AppenderRef ref="AsyncWarn"/>
            <AppenderRef ref="AsyncError"/>
        </Root>

    </Loggers>
</Configuration>
